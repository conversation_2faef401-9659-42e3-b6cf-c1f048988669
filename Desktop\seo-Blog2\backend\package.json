{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^6.4.0", "body-parser": "^1.19.0", "cookie-parser": "^1.4.4", "cors": "^2.8.5", "dotenv": "^8.1.0", "express": "^4.17.1", "express-jwt": "^5.3.1", "express-validator": "^6.1.1", "formidable": "^1.2.1", "google-auth-library": "^5.2.1", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "mongoose": "^5.6.10", "morgan": "^1.9.1", "node-fetch": "^2.6.0", "nodemon": "^1.19.1", "shortid": "^2.2.14", "slugify": "^1.3.4", "string-strip-html": "^4.1.1"}}